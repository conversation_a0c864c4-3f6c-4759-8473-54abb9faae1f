import React, { useState, useEffect } from 'react';
import YueModal from '@/components/YueModal/YueModal';
import { api } from '@/services';
import type { Track } from '@/types/api';
import { formatNumber } from '@/utils/utils';
import defaultAvatar from '@/assets/images/default-avatar.png';
import rectangle1 from '@/assets/images/rectangle-1.png';

interface ArtistModalProps {
  open: boolean;
  onClose: () => void;
  artistId?: string;
}

interface ArtistInfo {
  id: string;
  name: string;
  avatar: string;
  bio: string;
  releasedCount: number;
  revenue: number;
  streams: number;
}

const ArtistModal: React.FC<ArtistModalProps> = ({
  open,
  onClose,
  artistId = 'default-artist-id',
}) => {
  const [tracks, setTracks] = useState<Track[]>([]);
  const [loading, setLoading] = useState(false);
  const [artistInfo, setArtistInfo] = useState<ArtistInfo>({
    name: 'たかなし やすはる',
    avatar: defaultAvatar,
    id: artistId,
    bio: '<PERSON><PERSON>, born on April 13, 1963 in Tokyo, Japan, is a Japanese composer, arranger, keyboard player, and a member of the Japanese hard rock band "Musashi". He is currently mainly engaged in the production of animation music. The classic tracks in a series of well-known animations and TV works such as "Naruto", "Pretty Cure", "Fairy Tail", "Hell Girl", "Super Star God", and "Log Horizon" are all from his hand.',
    releasedCount: 120,
    revenue: 1234560, // 以分为单位
    streams: 246906,
  });

  // 获取艺术家信息
  const fetchArtistInfo = async () => {
    const response = await api.music.getArtistInfo(artistId);
    if (response.code === 200) {
      setArtistInfo(response.body);
    }
  };

  // 获取艺术家音乐列表
  useEffect(() => {
    if (open && artistId) {
      fetchArtistTracks();
    }
  }, [open, artistId]);

  const fetchArtistTracks = async () => {
    try {
      setLoading(true);
      // const response = await api.music.getTracksByArtist(artistId, 1, 30);
      // if (response.code === 200) {
      //   setTracks(response.body.result || []);
      // }

      // 使用模拟数据
      setTracks([
        {
          id: '1',
          title: 'Midnight City Lights',
          artistId: artistId,
          artistStageName: artistInfo.name,
          genre: 'Electronic',
          coverArtUrl: rectangle1,
          periodicRevenue: 50000,
          totalRevenue: 200000,
          periodicStreams: 15000,
          totalStreams: 80000,
          lastUpdate: null,
        },
        {
          id: '2',
          title: 'Slow Poison',
          artistId: artistId,
          artistStageName: artistInfo.name,
          genre: 'Ambient',
          coverArtUrl: rectangle1,
          periodicRevenue: 30000,
          totalRevenue: 150000,
          periodicStreams: 12000,
          totalStreams: 65000,
          lastUpdate: null,
        },
      ]);
    } catch (error) {
      console.error('Failed to fetch artist tracks:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <YueModal
      zIndex={801}
      open={open}
      onClose={onClose}
      centered={false}
      title="Artist Profile"
      width={1000}
      className="artist-modal"
    >
      <div className="flex gap-8 text-white pt-47px">
        {/* 左侧：艺术家头像 */}
        <div className="flex-shrink-0">
          <img
            className="w-230px h-230px bg-cover bg-center bg-no-repeat rounded-full"
            src={artistInfo.avatar}
            alt={artistInfo.name}
          />
        </div>

        {/* 右侧：艺术家信息 */}
        <div className="flex-1 space-y-6">
          {/* 艺术家名称 */}
          <h2 className="text-26px font-medium text-white leading-none">
            {artistInfo.name}
          </h2>

          {/* 统计信息 */}
          <div className="flex gap-8 text-12px">
            <div className="flex gap-1.5">
              <span className="text-label">Released:</span>
              <span className="text-white">{formatNumber artistInfo.releasedCount}</span>
            </div>
            <div className="flex gap-1.5">
              <span className="text-label">Revenue:</span>
              <span className="text-white">
                {formatNumber(artistInfo.revenue)}
              </span>
            </div>
            <div className="flex gap-1.5">
              <span className="text-label">Streams:</span>
              <span className="text-white">
                {formatNumber(artistInfo.streams)}
              </span>
            </div>
          </div>

          {/* 艺术家简介 */}
          <div className="space-y-2">
            <div className="text-12px text-label">Artist Bio:</div>
            <div className="text-12px text-white leading-normal max-h-32 overflow-y-auto">
              {artistInfo.bio}
            </div>
          </div>
        </div>
      </div>

      {/* 发布的音乐列表 */}
      <div className="mt-8">
        <h3 className="text-18px font-medium text-primary mb-4">
          Released Tracks
        </h3>

        {/* 表格头部 */}
        <div className="flex items-center gap-4 text-12px text-label  15 bg-#151515">
          <div className="w-12"></div>
          <div className="flex-1">Title</div>
          <div className="w-24 text-center">Genre</div>
          <div className="w-24 text-center">Revenue</div>
          <div className="w-24 text-center">Streams</div>
        </div>

        {/* 音乐列表 */}
        <div className="space-y-0 max-h-96 overflow-y-auto">
          {loading ? (
            <div className="text-center py-8 text-label">Loading...</div>
          ) : tracks.length > 0 ? (
            tracks.map(track => (
              <div
                key={track.id}
                className="flex items-center gap-4 py-3 text-12px hover:bg-#1a1a1a transition-colors"
              >
                {/* 封面 */}
                <div
                  className="w-12 h-12 bg-cover bg-center bg-no-repeat rounded flex-shrink-0"
                  style={{
                    backgroundImage: `url('${track.coverArtUrl}')`,
                    backgroundColor: '#333',
                  }}
                />

                {/* 标题和艺术家 */}
                <div className="flex-1 min-w-0">
                  <div className="text-white text-14px font-medium truncate">
                    {track.title}
                  </div>
                  <div className="text-label text-11px truncate">
                    {track.artistStageName}
                  </div>
                </div>

                {/* 类型 */}
                <div className="w-24 text-center text-label">{track.genre}</div>
                {/* 收入 */}
                <div className="w-24 text-center text-label">
                  {formatNumber(track.totalRevenue)}
                </div>

                {/* 播放 */}
                <div className="w-24 text-center text-label">
                  {formatNumber(track.totalStreams)}
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8 text-label">No tracks found</div>
          )}
        </div>
      </div>
    </YueModal>
  );
};

export default ArtistModal;
