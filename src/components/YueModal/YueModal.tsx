import React from 'react';
import { Modal } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { useLanguage } from '@/hooks/useLanguage';
import { type ModalProps } from 'antd';

interface YueModalProps extends ModalProps {
  open: boolean;
  onClose: () => void;
  children: React.ReactNode;
  title: string;
  minWidth?: number;
}

const YueModal: React.FC<YueModalProps> = ({
  open,
  onClose,
  children,
  title,
  width = 1000,
  centered = true,
  footer = null,
  keyboard = false,
  maskClosable = false,
  className = '',
  ...restProps
}) => {
  const { t } = useLanguage();

  return (
    <Modal
      open={open}
      onCancel={() => {
        onClose();
      }}
      width={width}
      className={
        '[&.ant-modal_.ant-modal-close]:(inset-ie-auto ml-12px top-40px) [&.ant-modal_.ant-modal-content]:(px-50px pt-35px pb-112px) ' +
        className
      }
      footer={footer}
      keyboard={keyboard}
      maskClosable={maskClosable}
      centered={centered}
      zIndex={800}
      closeIcon={
        <div className="flex items-center gap-2 hover:opacity-45">
          <ArrowLeftOutlined
            style={{ fontSize: '16px', color: 'var(--color-label)' }}
          />
          <span className="text-#B5B5B5 font-400 min-w-max">
            {t('common.back')}
          </span>
        </div>
      }
      title={
        <div className=" text-center text-white text-32px font-700">
          {title}
        </div>
      }
      {...restProps}
    >
      {children}
    </Modal>
  );
};

export default YueModal;
